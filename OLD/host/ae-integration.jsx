/**
 * SahAI CEP Extension V2 - ExtendScript Main File
 * This file handles communication between the CEP panel and Adobe applications
 */

// Global namespace for SahAI ExtendScript functions
var SahAI = SahAI || {};

/**
 * Initialize the ExtendScript environment
 */
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging
        
        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");
        
        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get application information
 */
SahAI.getAppInfo = function() {
    try {
        return {
            success: true,
            data: {
                name: app.name,
                version: app.version,
                locale: app.locale,
                build: app.build || "Unknown"
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Execute code in the host application
 * @param {string} code - The code to execute
 * @param {string} language - The programming language (for context)
 */
SahAI.executeCode = function(code, language) {
    try {
        var result;
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'extendscript':
                // Execute ExtendScript code
                result = eval(code);
                break;
                
            case 'applescript':
                // Execute AppleScript (macOS only)
                if ($.os.indexOf("Mac") !== -1) {
                    result = app.doScript(code, ScriptLanguage.APPLESCRIPT_LANGUAGE);
                } else {
                    throw new Error("AppleScript is only supported on macOS");
                }
                break;
                
            case 'vbscript':
                // Execute VBScript (Windows only)
                if ($.os.indexOf("Win") !== -1) {
                    result = app.doScript(code, ScriptLanguage.VISUAL_BASIC);
                } else {
                    throw new Error("VBScript is only supported on Windows");
                }
                break;
                
            default:
                throw new Error("Unsupported language: " + language);
        }
        
        return {
            success: true,
            result: result ? result.toString() : "Code executed successfully",
            language: language
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString(),
            language: language
        };
    }
};

/**
 * Get document information
 */
SahAI.getDocumentInfo = function() {
    try {
        if (!app.activeDocument) {
            return {
                success: false,
                message: "No active document"
            };
        }
        
        var doc = app.activeDocument;
        return {
            success: true,
            data: {
                name: doc.name,
                path: doc.fullName ? doc.fullName.toString() : "Unsaved",
                saved: doc.saved,
                modified: doc.modified || false
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Show alert dialog
 * @param {string} message - The message to display
 * @param {string} title - The dialog title
 */
SahAI.showAlert = function(message, title) {
    try {
        title = title || "SahAI";
        alert(message, title);
        return {
            success: true,
            message: "Alert displayed"
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Log message to ExtendScript console
 * @param {string} message - The message to log
 * @param {string} level - Log level (info, warn, error)
 */
SahAI.log = function(message, level) {
    try {
        level = level || "info";
        var timestamp = new Date().toISOString();
        var logMessage = "[" + timestamp + "] [" + level.toUpperCase() + "] " + message;
        
        $.writeln(logMessage);
        
        return {
            success: true,
            message: "Logged: " + message
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get system information
 */
SahAI.getSystemInfo = function() {
    try {
        return {
            success: true,
            data: {
                os: $.os,
                version: $.version,
                buildDate: $.buildDate,
                locale: $.locale,
                memoryUsage: $.memCache
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Load settings from file
 */
function loadSettings() {
    try {
        var settingsFile = new File("~/Adobe/CEP/extensions/SahAI/settings.json");
        if (settingsFile.exists) {
            settingsFile.open("r");
            var content = settingsFile.read();
            settingsFile.close();
            return content;
        }
        return "{}";
    } catch (error) {
        $.writeln("Error loading settings: " + error.toString());
        return "{}";
    }
}

/**
 * Save settings to file
 * @param {Object} settings - Settings object to save
 */
function saveSettings(settings) {
    try {
        var settingsDir = new Folder("~/Adobe/CEP/extensions/SahAI");
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var settingsFile = new File("~/Adobe/CEP/extensions/SahAI/settings.json");
        settingsFile.open("w");
        settingsFile.write(JSON.stringify(settings));
        settingsFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Load chat history from file
 */
function loadHistory() {
    try {
        var historyFile = new File("~/Adobe/CEP/extensions/SahAI/history.json");
        if (historyFile.exists) {
            historyFile.open("r");
            var content = historyFile.read();
            historyFile.close();
            return JSON.stringify({ success: true, data: JSON.parse(content) });
        }
        return JSON.stringify({ success: true, data: [] });
    } catch (error) {
        $.writeln("Error loading history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: [] });
    }
}

/**
 * Save chat history to file
 * @param {Array} history - Array of chat sessions to save
 */
function saveHistory(history) {
    try {
        var settingsDir = new Folder("~/Adobe/CEP/extensions/SahAI");
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var historyFile = new File("~/Adobe/CEP/extensions/SahAI/history.json");
        historyFile.open("w");
        historyFile.write(JSON.stringify(history));
        historyFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Get OpenAI model description
 * @param {string} modelId - Model identifier
 * @returns {string} Model description
 */
function getOpenAIModelDescription(modelId) {
    var descriptions = {
        'gpt-4o': 'Most capable OpenAI model',
        'gpt-4o-mini': 'Faster, more affordable',
        'gpt-4-turbo': 'Previous generation flagship',
        'gpt-3.5-turbo': 'Legacy model'
    };
    return descriptions[modelId] || '';
}

/**
 * Get OpenAI model context length
 * @param {string} modelId - Model identifier
 * @returns {number} Context length
 */
function getOpenAIModelContextLength(modelId) {
    var contextLengths = {
        'gpt-4o': 128000,
        'gpt-4o-mini': 128000,
        'gpt-4-turbo': 128000,
        'gpt-3.5-turbo': 16384
    };
    return contextLengths[modelId] || 4096;
}

/**
 * Get Groq model description
 * @param {string} modelId - Model identifier
 * @returns {string} Model description
 */
function getGroqModelDescription(modelId) {
    var descriptions = {
        'llama-3.1-8b-instant': 'Fast inference',
        'llama-3.1-70b-versatile': 'Balanced performance',
        'mixtral-8x7b-32768': 'Large context'
    };
    return descriptions[modelId] || '';
}

/**
 * Get Groq model context length
 * @param {string} modelId - Model identifier
 * @returns {number} Context length
 */
function getGroqModelContextLength(modelId) {
    var contextLengths = {
        'llama-3.1-8b-instant': 131072,
        'llama-3.1-70b-versatile': 131072,
        'mixtral-8x7b-32768': 32768
    };
    return contextLengths[modelId] || 8192;
}

/**
 * Get Mistral model description
 * @param {string} modelId - Model identifier
 * @returns {string} Model description
 */
function getMistralModelDescription(modelId) {
    var descriptions = {
        'mistral-large-latest': 'Most capable',
        'mistral-medium-latest': 'Balanced',
        'mistral-small-latest': 'Fast and efficient'
    };
    return descriptions[modelId] || '';
}

/**
 * Get Mistral model context length
 * @param {string} modelId - Model identifier
 * @returns {number} Context length
 */
function getMistralModelContextLength(modelId) {
    var contextLengths = {
        'mistral-large-latest': 128000,
        'mistral-medium-latest': 32000,
        'mistral-small-latest': 32000
    };
    return contextLengths[modelId] || 32000;
}

/**
 * List models for different providers
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function listModels(providerId, baseURL, apiKey) {
    var result = { ok: false, models: [] };
    
    try {
        switch (providerId) {
            case 'ollama':
                var ollamaUrl = baseURL || "http://localhost:11434";
                $.writeln("Fetching Ollama models from: " + ollamaUrl);
                var ollamaRes = getURL(ollamaUrl + "/api/tags", null, 8000, 1); // Shorter timeout for local
                if (ollamaRes.success) {
                    try {
                        var data = JSON.parse(ollamaRes.data);
                        if (data.models && Array.isArray(data.models)) {
                            result = { ok: true, models: data.models.map(function(m) {
                                return {
                                    id: m.name,
                                    name: m.name,
                                    description: 'Local Ollama model - ' + (m.details ? m.details.parameter_size || 'Unknown size' : 'Unknown size'),
                                    context_length: 4096,
                                    is_recommended: false
                                };
                            })};
                            $.writeln("Successfully loaded " + data.models.length + " Ollama models");
                        } else {
                            throw new Error("Invalid response format from Ollama API");
                        }
                    } catch (parseError) {
                        $.writeln("Error parsing Ollama response: " + parseError.toString());
                        throw parseError;
                    }
                } else {
                    throw new Error("Ollama API request failed: " + (ollamaRes.message || "Unknown error"));
                }
                break;

            case 'lmstudio':
                var lmstudioUrl = baseURL || "http://localhost:1234";
                $.writeln("Fetching LM Studio models from: " + lmstudioUrl);
                var lmstudioRes = getURL(lmstudioUrl + "/v1/models", null, 8000, 1); // Shorter timeout for local
                if (lmstudioRes.success) {
                    try {
                        var data = JSON.parse(lmstudioRes.data);
                        if (data.data && Array.isArray(data.data)) {
                            result = { ok: true, models: data.data.map(function(m) {
                                return {
                                    id: m.id,
                                    name: m.id,
                                    description: 'Local LM Studio model - ' + (m.owned_by || 'Unknown'),
                                    context_length: m.context_length || 4096,
                                    is_recommended: false
                                };
                            })};
                            $.writeln("Successfully loaded " + data.data.length + " LM Studio models");
                        } else {
                            throw new Error("Invalid response format from LM Studio API");
                        }
                    } catch (parseError) {
                        $.writeln("Error parsing LM Studio response: " + parseError.toString());
                        throw parseError;
                    }
                } else {
                    throw new Error("LM Studio API request failed: " + (lmstudioRes.message || "Unknown error"));
                }
                break;

            case 'anthropic':
                if (!apiKey || apiKey.trim() === '') {
                    throw new Error("API key is required for Anthropic");
                }
                $.writeln("Fetching Anthropic models with API key: " + apiKey.substring(0, 10) + "...");
                var anthropicRes = getURL("https://api.anthropic.com/v1/models", {
                    "x-api-key": apiKey,
                    "anthropic-version": "2023-06-01"
                }, 12000, 2); // Longer timeout for API calls
                if (anthropicRes.success) {
                    try {
                        var data = JSON.parse(anthropicRes.data);
                        if (data.data && Array.isArray(data.data)) {
                            result = { ok: true, models: data.data.map(function(m) {
                                return {
                                    id: m.id,
                                    name: m.display_name || m.id,
                                    description: m.description || 'Anthropic Claude model',
                                    context_length: m.context_length || 200000,
                                    is_recommended: m.id.indexOf('claude-3-5-sonnet') !== -1
                                };
                            })};
                            $.writeln("Successfully loaded " + data.data.length + " Anthropic models");
                        } else {
                            throw new Error("Invalid response format from Anthropic API");
                        }
                    } catch (parseError) {
                        $.writeln("Error parsing Anthropic response: " + parseError.toString());
                        throw parseError;
                    }
                } else {
                    throw new Error("Anthropic API request failed: " + (anthropicRes.message || "Unknown error"));
                }
                break;

            case 'openai':
                if (!apiKey || apiKey.trim() === '') {
                    throw new Error("API key is required for OpenAI");
                }
                $.writeln("Fetching OpenAI models with API key: " + apiKey.substring(0, 10) + "...");
                var headers = {
                    "Authorization": "Bearer " + apiKey,
                    "OpenAI-Beta": "assistants=v2"
                };
                var openaiRes = getURL("https://api.openai.com/v1/models", headers, 12000, 2);
                if (openaiRes.success) {
                    try {
                        var data = JSON.parse(openaiRes.data);
                        if (data.data && Array.isArray(data.data)) {
                            result = { ok: true, models: data.data.map(function(m) {
                                return {
                                    id: m.id,
                                    name: m.id,
                                    description: getOpenAIModelDescription(m.id),
                                    context_length: getOpenAIModelContextLength(m.id),
                                    is_recommended: m.id === 'gpt-4o' || m.id === 'gpt-4o-mini'
                                };
                            })};
                            $.writeln("Successfully loaded " + data.data.length + " OpenAI models");
                        } else {
                            throw new Error("Invalid response format from OpenAI API");
                        }
                    } catch (parseError) {
                        $.writeln("Error parsing OpenAI response: " + parseError.toString());
                        throw parseError;
                    }
                } else {
                    throw new Error("OpenAI API request failed: " + (openaiRes.message || "Unknown error"));
                }
                break;

            case 'gemini':
                result = { ok: true, models: [
                    {
                        id: 'gemini-1.5-pro',
                        name: 'Gemini 1.5 Pro',
                        description: 'Google\'s most capable model',
                        context_length: 2000000,
                        is_recommended: true
                    },
                    {
                        id: 'gemini-1.5-flash',
                        name: 'Gemini 1.5 Flash',
                        description: 'Fast and efficient',
                        context_length: 1000000,
                        is_recommended: false
                    }
                ]};
                break;

            case 'groq':
                var groqHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    groqHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var groqRes = getURL("https://api.groq.com/openai/v1/models", groqHeaders);
                if (groqRes.success) {
                    var data = JSON.parse(groqRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: getGroqModelDescription(m.id),
                            context_length: getGroqModelContextLength(m.id),
                            is_recommended: m.id.indexOf('llama-3.1-70b') !== -1
                        };
                    })};
                }
                break;

            case 'deepseek':
                var deepseekHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    deepseekHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var deepseekRes = getURL("https://api.deepseek.com/v1/models", deepseekHeaders);
                if (deepseekRes.success) {
                    var data = JSON.parse(deepseekRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: m.id.indexOf('chat') !== -1 ? 'General purpose' : 'Code-focused',
                            context_length: 128000,
                            is_recommended: m.id === 'deepseek-chat'
                        };
                    })};
                }
                break;

            case 'mistral':
                var mistralHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    mistralHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var mistralRes = getURL("https://api.mistral.ai/v1/models", mistralHeaders);
                if (mistralRes.success) {
                    var data = JSON.parse(mistralRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: getMistralModelDescription(m.id),
                            context_length: getMistralModelContextLength(m.id),
                            is_recommended: m.id.indexOf('large') !== -1
                        };
                    })};
                }
                break;

            case 'moonshot':
                var moonshotHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    moonshotHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var moonshotRes = getURL("https://api.moonshot.cn/v1/models", moonshotHeaders);
                if (moonshotRes.success) {
                    var data = JSON.parse(moonshotRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: m.id.indexOf('moonshot') !== -1 ? 'Moonshot AI model' : '',
                            context_length: 200000,
                            is_recommended: m.id.indexOf('v1-128k') !== -1
                        };
                    })};
                }
                break;

            case 'openrouter':
                var openrouterHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    openrouterHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var openrouterRes = getURL("https://openrouter.ai/api/v1/models", openrouterHeaders);
                if (openrouterRes.success) {
                    var data = JSON.parse(openrouterRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.name || m.id,
                            description: m.description || '',
                            context_length: m.context_length || 4096,
                            is_recommended: false
                        };
                    })};
                }
                break;

            case 'perplexity':
                var perplexityHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    perplexityHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var perplexityRes = getURL("https://api.perplexity.ai/models", perplexityHeaders);
                if (perplexityRes.success) {
                    var data = JSON.parse(perplexityRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: 'Perplexity AI model',
                            context_length: 128000,
                            is_recommended: m.id.indexOf('large') !== -1
                        };
                    })};
                } else {
                    // Fallback to known Perplexity models
                    result = { ok: true, models: [
                        {
                            id: 'llama-3.1-sonar-small-128k-online',
                            name: 'Llama 3.1 Sonar Small 128K Online',
                            description: 'Small online model',
                            context_length: 128000,
                            is_recommended: false
                        },
                        {
                            id: 'llama-3.1-sonar-large-128k-online',
                            name: 'Llama 3.1 Sonar Large 128K Online',
                            description: 'Large online model',
                            context_length: 128000,
                            is_recommended: true
                        },
                        {
                            id: 'llama-3.1-sonar-huge-128k-online',
                            name: 'Llama 3.1 Sonar Huge 128K Online',
                            description: 'Huge online model',
                            context_length: 128000,
                            is_recommended: false
                        }
                    ]};
                }
                break;

            case 'qwen':
                var qwenHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    qwenHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var qwenRes = getURL("https://dashscope.aliyuncs.com/api/v1/models", qwenHeaders);
                if (qwenRes.success) {
                    var data = JSON.parse(qwenRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: 'Alibaba Qwen model',
                            context_length: 32000,
                            is_recommended: m.id === 'qwen-max'
                        };
                    })};
                } else {
                    // Fallback to known Qwen models
                    result = { ok: true, models: [
                        {
                            id: 'qwen-turbo',
                            name: 'Qwen Turbo',
                            description: 'Fast and efficient',
                            context_length: 8000,
                            is_recommended: false
                        },
                        {
                            id: 'qwen-plus',
                            name: 'Qwen Plus',
                            description: 'Balanced performance',
                            context_length: 32000,
                            is_recommended: false
                        },
                        {
                            id: 'qwen-max',
                            name: 'Qwen Max',
                            description: 'Most capable',
                            context_length: 32000,
                            is_recommended: true
                        }
                    ]};
                }
                break;

            case 'together':
                var togetherHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    togetherHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var togetherRes = getURL("https://api.together.xyz/v1/models", togetherHeaders);
                if (togetherRes.success) {
                    var data = JSON.parse(togetherRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.display_name || m.id,
                            description: m.description || 'Together AI model',
                            context_length: m.context_length || 4096,
                            is_recommended: false
                        };
                    })};
                }
                break;

            case 'vertex':
                // Vertex AI requires more complex authentication, fallback to known models
                result = { ok: true, models: [
                    {
                        id: 'gemini-1.5-pro',
                        name: 'Gemini 1.5 Pro',
                        description: 'Google\'s most capable model',
                        context_length: 2000000,
                        is_recommended: true
                    },
                    {
                        id: 'gemini-1.5-flash',
                        name: 'Gemini 1.5 Flash',
                        description: 'Fast and efficient',
                        context_length: 1000000,
                        is_recommended: false
                    },
                    {
                        id: 'gemini-1.0-pro',
                        name: 'Gemini 1.0 Pro',
                        description: 'Previous generation',
                        context_length: 32000,
                        is_recommended: false
                    }
                ]};
                break;

            case 'xai':
                var xaiHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    xaiHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var xaiRes = getURL("https://api.x.ai/v1/models", xaiHeaders);
                if (xaiRes.success) {
                    var data = JSON.parse(xaiRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return {
                            id: m.id,
                            name: m.id,
                            description: 'xAI Grok model',
                            context_length: 128000,
                            is_recommended: m.id === 'grok-beta'
                        };
                    })};
                } else {
                    // Fallback to known xAI models
                    result = { ok: true, models: [
                        {
                            id: 'grok-beta',
                            name: 'Grok Beta',
                            description: 'xAI\'s flagship model',
                            context_length: 128000,
                            is_recommended: true
                        },
                        {
                            id: 'grok-vision-beta',
                            name: 'Grok Vision Beta',
                            description: 'Vision-capable model',
                            context_length: 128000,
                            is_recommended: false
                        }
                    ]};
                }
                break;

            default:
                result = { ok: true, models: [] };
        }
    } catch (e) {
        $.writeln("Error listing models for " + providerId + ": " + e.toString());

        // Provide more detailed error information
        var errorMessage = e.toString();
        var friendlyError = errorMessage;

        if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
            friendlyError = 'Request timed out. Please check your internet connection.';
        } else if (errorMessage.indexOf('network') !== -1 || errorMessage.indexOf('connection') !== -1) {
            friendlyError = 'Network error. Please check your internet connection.';
        } else if (errorMessage.indexOf('401') !== -1 || errorMessage.indexOf('unauthorized') !== -1) {
            friendlyError = 'Invalid API key. Please check your credentials.';
        } else if (errorMessage.indexOf('403') !== -1 || errorMessage.indexOf('forbidden') !== -1) {
            friendlyError = 'Access denied. Please check your API key permissions.';
        } else if (errorMessage.indexOf('404') !== -1 || errorMessage.indexOf('not found') !== -1) {
            friendlyError = 'API endpoint not found. Please check the provider configuration.';
        } else if (errorMessage.indexOf('500') !== -1) {
            friendlyError = 'Server error. Please try again later.';
        }

        // Provide fallback models for each provider when API fails
        var fallbackModels = getFallbackModels(providerId);
        if (fallbackModels.length > 0) {
            result = { ok: true, models: fallbackModels, fallback: true, originalError: friendlyError };
            $.writeln("Using fallback models for " + providerId + " due to: " + friendlyError);
        } else {
            result = { ok: false, models: [], error: friendlyError };
        }
    }

    // Final safety check - ensure we always return valid models array
    if (!result.models || !Array.isArray(result.models)) {
        result.models = [];
    }

    return JSON.stringify(result);
}

/**
 * Get fallback models when API calls fail
 * @param {string} providerId - Provider identifier
 * @returns {Array} Array of fallback models
 */
function getFallbackModels(providerId) {
    var fallbackModels = {
        'openai': [
            { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable OpenAI model', context_length: 128000, is_recommended: true },
            { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster, more affordable', context_length: 128000, is_recommended: false },
            { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Previous generation flagship', context_length: 128000, is_recommended: false },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Legacy model', context_length: 16384, is_recommended: false }
        ],
        'anthropic': [
            { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Anthropic\'s most capable model', context_length: 200000, is_recommended: true },
            { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku', description: 'Fast and efficient', context_length: 200000, is_recommended: false },
            { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Powerful reasoning', context_length: 200000, is_recommended: false }
        ],
        'gemini': [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Google\'s most capable model', context_length: 2000000, is_recommended: true },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient', context_length: 1000000, is_recommended: false },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Previous generation', context_length: 32000, is_recommended: false }
        ],
        'groq': [
            { id: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B', description: 'Balanced performance', context_length: 131072, is_recommended: true },
            { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B', description: 'Fast inference', context_length: 131072, is_recommended: false },
            { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', description: 'Large context', context_length: 32768, is_recommended: false }
        ],
        'deepseek': [
            { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'General purpose', context_length: 128000, is_recommended: true },
            { id: 'deepseek-coder', name: 'DeepSeek Coder', description: 'Code-focused', context_length: 128000, is_recommended: false }
        ],
        'mistral': [
            { id: 'mistral-large-latest', name: 'Mistral Large', description: 'Most capable', context_length: 128000, is_recommended: true },
            { id: 'mistral-medium-latest', name: 'Mistral Medium', description: 'Balanced', context_length: 32000, is_recommended: false },
            { id: 'mistral-small-latest', name: 'Mistral Small', description: 'Fast and efficient', context_length: 32000, is_recommended: false }
        ],
        'moonshot': [
            { id: 'moonshot-v1-8k', name: 'Moonshot v1 8K', description: 'Small context', context_length: 8000, is_recommended: false },
            { id: 'moonshot-v1-32k', name: 'Moonshot v1 32K', description: 'Medium context', context_length: 32000, is_recommended: false },
            { id: 'moonshot-v1-128k', name: 'Moonshot v1 128K', description: 'Large context', context_length: 128000, is_recommended: true }
        ],
        'openrouter': [
            { id: 'openai/gpt-4o', name: 'GPT-4o (OpenRouter)', description: 'OpenAI via OpenRouter', context_length: 128000, is_recommended: true },
            { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet (OpenRouter)', description: 'Anthropic via OpenRouter', context_length: 200000, is_recommended: false },
            { id: 'meta-llama/llama-3.1-70b-instruct', name: 'Llama 3.1 70B (OpenRouter)', description: 'Meta via OpenRouter', context_length: 131072, is_recommended: false }
        ],
        'perplexity': [
            { id: 'llama-3.1-sonar-small-128k-online', name: 'Llama 3.1 Sonar Small 128K Online', description: 'Small online model', context_length: 128000, is_recommended: false },
            { id: 'llama-3.1-sonar-large-128k-online', name: 'Llama 3.1 Sonar Large 128K Online', description: 'Large online model', context_length: 128000, is_recommended: true },
            { id: 'llama-3.1-sonar-huge-128k-online', name: 'Llama 3.1 Sonar Huge 128K Online', description: 'Huge online model', context_length: 128000, is_recommended: false }
        ],
        'qwen': [
            { id: 'qwen-turbo', name: 'Qwen Turbo', description: 'Fast and efficient', context_length: 8000, is_recommended: false },
            { id: 'qwen-plus', name: 'Qwen Plus', description: 'Balanced performance', context_length: 32000, is_recommended: false },
            { id: 'qwen-max', name: 'Qwen Max', description: 'Most capable', context_length: 32000, is_recommended: true }
        ],
        'together': [
            { id: 'meta-llama/Llama-3-70b-chat-hf', name: 'Llama 3 70B Chat', description: 'Large language model', context_length: 8192, is_recommended: true },
            { id: 'meta-llama/Llama-3-8b-chat-hf', name: 'Llama 3 8B Chat', description: 'Smaller, faster model', context_length: 8192, is_recommended: false },
            { id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', name: 'Mixtral 8x7B Instruct', description: 'Mixture of experts', context_length: 32768, is_recommended: false }
        ],
        'vertex': [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Google\'s most capable model', context_length: 2000000, is_recommended: true },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient', context_length: 1000000, is_recommended: false },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Previous generation', context_length: 32000, is_recommended: false }
        ],
        'xai': [
            { id: 'grok-beta', name: 'Grok Beta', description: 'xAI\'s flagship model', context_length: 128000, is_recommended: true },
            { id: 'grok-vision-beta', name: 'Grok Vision Beta', description: 'Vision-capable model', context_length: 128000, is_recommended: false }
        ],
        'ollama': [
            { id: 'llama3.1', name: 'Llama 3.1', description: 'Open source LLM', context_length: 4096, is_recommended: true },
            { id: 'mistral', name: 'Mistral', description: 'Efficient transformer', context_length: 8192, is_recommended: false },
            { id: 'codellama', name: 'Code Llama', description: 'Code-focused', context_length: 16384, is_recommended: false }
        ],
        'lmstudio': [
            { id: 'local-model', name: 'Local Model', description: 'Your local model', context_length: 4096, is_recommended: true }
        ]
    };

    return fallbackModels[providerId] || [];
}

/**
 * Helper function to make HTTP requests with timeout and better error handling
 * @param {string} url - URL to fetch
 * @param {Object} headers - Optional headers
 * @param {number} timeout - Timeout in milliseconds (default: 10000)
 * @param {number} retries - Number of retry attempts (default: 2)
 */
function getURL(url, headers, timeout, retries) {
    timeout = timeout || 10000; // Default 10 second timeout (reduced for better UX)
    retries = retries || 2; // Default 2 retries

    var lastError = null;

    for (var attempt = 0; attempt <= retries; attempt++) {
        try {
            $.writeln("Making HTTP request to: " + url + " (attempt " + (attempt + 1) + "/" + (retries + 1) + ")");

            var http = new XMLHttpRequest();
            var startTime = new Date().getTime();

            // Enhanced timeout handling for ExtendScript
            var timeoutId = null;
            var timedOut = false;

            // Manual timeout implementation since XMLHttpRequest.timeout may not work
            if (timeout > 0) {
                timeoutId = setTimeout(function() {
                    timedOut = true;
                    try {
                        http.abort();
                    } catch (e) {
                        // Ignore abort errors
                    }
                }, timeout);
            }

            http.open("GET", url, false); // Synchronous request

            // Set default headers with better compatibility
            try {
                http.setRequestHeader("User-Agent", "SahAI-CEP-Extension/2.0");
                http.setRequestHeader("Accept", "application/json");
                http.setRequestHeader("Cache-Control", "no-cache");

                // Add CORS headers for local providers
                if (url.indexOf('localhost') !== -1 || url.indexOf('127.0.0.1') !== -1) {
                    http.setRequestHeader("Access-Control-Allow-Origin", "*");
                }
            } catch (headerError) {
                $.writeln("Warning: Could not set some headers: " + headerError.toString());
            }

            // Set custom headers
            if (headers) {
                for (var key in headers) {
                    if (headers.hasOwnProperty(key)) {
                        try {
                            http.setRequestHeader(key, headers[key]);
                        } catch (headerError) {
                            $.writeln("Warning: Could not set header " + key + ": " + headerError.toString());
                        }
                    }
                }
            }

            // Send request
            http.send();

            // Clear timeout
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // Check if request timed out
            if (timedOut) {
                throw new Error("Request timed out after " + timeout + "ms");
            }

            var endTime = new Date().getTime();
            var duration = endTime - startTime;
            $.writeln("HTTP response status: " + http.status + " (took " + duration + "ms)");

            if (http.status >= 200 && http.status < 300) {
                return { success: true, data: http.responseText, duration: duration };
            } else if (http.status === 401) {
                return { success: false, message: "Unauthorized - Invalid API key" };
            } else if (http.status === 403) {
                return { success: false, message: "Forbidden - Access denied" };
            } else if (http.status === 404) {
                return { success: false, message: "Not Found - API endpoint not found" };
            } else if (http.status === 429) {
                return { success: false, message: "Rate Limited - Too many requests" };
            } else if (http.status >= 500) {
                return { success: false, message: "Server Error - Please try again later" };
            } else {
                throw new Error("HTTP " + http.status + " - " + http.statusText);
            }
        } catch (error) {
            lastError = error;
            var errorMessage = error.toString();
            $.writeln("HTTP request error (attempt " + (attempt + 1) + "): " + errorMessage);

            // Don't retry on certain errors
            if (errorMessage.indexOf('401') !== -1 || errorMessage.indexOf('403') !== -1 ||
                errorMessage.indexOf('Unauthorized') !== -1 || errorMessage.indexOf('Forbidden') !== -1) {
                break; // Don't retry auth errors
            }

            // Wait before retry (exponential backoff)
            if (attempt < retries) {
                var delay = Math.min(1000 * Math.pow(2, attempt), 3000); // Max 3s delay
                $.writeln("Retrying in " + delay + "ms...");

                // Simple delay implementation for ExtendScript
                var startDelay = new Date().getTime();
                while (new Date().getTime() - startDelay < delay) {
                    // Busy wait (not ideal but works in ExtendScript)
                }
            }
        }
    }

    // All attempts failed
    var errorMessage = lastError ? lastError.toString() : "Unknown error";
    if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
        return { success: false, message: "Request timed out after " + timeout + "ms" };
    } else if (errorMessage.indexOf('network') !== -1 || errorMessage.indexOf('connection') !== -1) {
        return { success: false, message: "Network connection error" };
    } else {
        return { success: false, message: errorMessage };
    }
}

/**
 * Fetch models for a specific provider (wrapper for listModels)
 * This function is called by the ProviderBridge in cepIntegration.ts
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function fetchModels(providerId, baseURL, apiKey) {
    try {
        $.writeln("fetchModels called with providerId: " + providerId + ", baseURL: " + (baseURL || 'default') + ", apiKey: " + (apiKey ? 'provided' : 'not provided'));

        // Call the existing listModels function
        var result = listModels(providerId, baseURL, apiKey);
        var parsedResult = JSON.parse(result);

        // Transform the result to match expected format
        if (parsedResult.ok) {
            return JSON.stringify(parsedResult.models);
        } else {
            return JSON.stringify({ error: parsedResult.error || 'Failed to fetch models' });
        }
    } catch (error) {
        $.writeln("Error in fetchModels: " + error.toString());
        return JSON.stringify({ error: error.toString() });
    }
}

// Initialize SahAI when script loads
try {
    $.writeln("=== SahAI Extension Loading ===");
    SahAI.init();
    $.writeln("=== SahAI Extension Loaded Successfully ===");
} catch (error) {
    $.writeln("=== SahAI Extension Load Error: " + error.toString() + " ===");
}
