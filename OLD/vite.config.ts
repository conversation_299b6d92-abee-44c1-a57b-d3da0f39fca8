// vite.config.ts (optimized for CEP)
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { viteSingleFile } from 'vite-plugin-singlefile';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react(), viteSingleFile()], // Inline assets for CEP
  base: './', // Relative paths for panel
  root: 'client', // source folder
  build: {
    target: 'es2020', // CEP Chromium compatibility
    outDir: resolve(__dirname, 'dist'), // 1. build into /dist
    emptyOutDir: true, // 2. clean before every build
    assetsInlineLimit: 0, // Inline all assets (e.g., onig.wasm)
    // Raise the warning threshold to monitor chunk sizes after optimization
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      output: {
        entryFileNames: 'assets/[name].js', // CEP-safe naming
        assetFileNames: 'assets/[name]-[hash].[ext]'
        // Note: manualChunks is not compatible with vite-plugin-singlefile's inlineDynamicImports
        // The plugin will inline everything into a single file for CEP compatibility
      },
      // Exclude unwanted Shiki language files from bundling
      external: (id) => {
        // Allow our essential languages
        const essentialLangs = [
          'javascript', 'typescript', 'jsx', 'tsx',
          'html', 'css', 'scss', 'less',
          'json', 'jsonc', 'xml', 'yaml',
          'markdown',
          'python', 'swift', 'rust', 'go', 'java', 'php', 'ruby', 'shell',
          'actionscript-3'
        ];

        // Check if this is a Shiki language file
        if (id.includes('@shikijs/langs/dist/') && id.endsWith('.mjs')) {
          const langName = id.split('/').pop()?.replace('.mjs', '');
          // Only bundle essential languages, exclude all others
          return !essentialLangs.includes(langName || '');
        }

        return false; // Don't exclude anything else
      }
    },
  },
  server: {
    port: 3000, // Dev server
    strictPort: true
  },
});