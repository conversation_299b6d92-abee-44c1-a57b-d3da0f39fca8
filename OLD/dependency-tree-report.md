# SahAI Dependency Tree Report

## Executive Summary
This report provides a comprehensive analysis of the SahAI project's dependency structure, execution paths, and file relationships. The project is a React-based Adobe CEP (Common Extensibility Platform) extension that provides AI chat capabilities with multiple provider integrations.

## Project Overview
- **Project Name**: SahAI CEP v2
- **Version**: 2.0.0
- **Type**: Adobe CEP Extension
- **Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 5.3.3

## Core Dependencies

### Runtime Dependencies
```json
{
  "lucide-react": "^0.408.0",
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "shiki": "^1.10.3",
  "zustand": "^4.5.2"
}
```

### Development Dependencies
```json
{
  "@types/react": "^18.3.3",
  "@types/react-dom": "^18.3.0",
  "@vitejs/plugin-react": "^4.3.1",
  "autoprefixer": "^10.4.19",
  "postcss": "^8.4.38",
  "tailwindcss": "^3.4.4",
  "typescript": "^5.4.5",
  "vite": "^5.3.3",
  "vite-plugin-singlefile": "^2.3.0"
}
```

## Application Architecture & Execution Flow

### 1. Entry Point (`client/src/main.tsx`)
**File**: `client/src/main.tsx`
**Purpose**: Application bootstrap and initialization

```typescript
// Execution sequence:
1. import React from 'react';
2. import ReactDOM from 'react-dom/client';
3. import App from './App';
4. import { initializeCEP } from './utils/cepIntegration';
5. import { useSettingsStore } from './components/stores/settingsStore';
6. import { useToastStore } from './components/stores/toastStore';

// Initialization flow:
initializeCEP(); // CEP environment setup
useSettingsStore.getState().loadSettings(); // Load persisted settings
useToastStore.getState(); // Initialize toast system
ReactDOM.createRoot(...).render(<App />); // Mount React app
```

### 2. Root Component (`client/src/App.tsx`)
**File**: `client/src/App.tsx`
**Purpose**: Main application layout and component orchestration

```typescript
// Component hierarchy:
<div className="flex flex-col h-screen">
  <TopBar />                    // Provider status & model selector
  <ChatMessages />             // Scrollable message thread
  <InputArea />                // Typing box with controls
  <ModalRoot />                // Global modal layer
  <ToastContainer />           // Toast notifications
</div>
```

### 3. CEP Integration Layer (`client/src/utils/cepIntegration.ts`)
**File**: `client/src/utils/cepIntegration.ts`
**Purpose**: Adobe CEP environment communication and ExtendScript bridge

**Key Classes & Functions**:
- `isCEPEnvironment()`: Detects CEP runtime
- `getCSInterface()`: Returns CSInterface instance
- `executeExtendScript()`: Promise-based ExtendScript execution
- `CEPSettings`: Persistent settings management
- `ProviderBridge`: Model listing and provider communication

### 4. State Management Architecture

#### 4.1 Settings Store (`client/src/components/stores/settingsStore.ts`)
**File**: `client/src/components/stores/settingsStore.ts`
**Purpose**: Global settings and provider configuration

**State Structure**:
```typescript
interface SettingsState {
  providers: Provider[];        // 15 AI providers
  activeProviderId: string | undefined;
  isLoadingModels: boolean;
}
```

**Key Methods**:
- `loadSettings()`: Load from CEP/localStorage
- `loadModelsForProvider()`: Fetch available models
- `saveProviderSelection()`: Persist provider config

#### 4.2 Chat Store (`client/src/components/stores/chatStore.ts`)
**File**: `client/src/components/stores/chatStore.ts`
**Purpose**: Chat session and message management

**State Structure**:
```typescript
interface ChatState {
  messages: Message[];
  isLoading: boolean;
  currentSession?: string;
}
```

#### 4.3 History Store (`client/src/components/stores/historyStore.ts`)
**File**: `client/src/components/stores/historyStore.ts`
**Purpose**: Chat session persistence and management

**State Structure**:
```typescript
interface HistoryState {
  sessions: ChatSession[];
  currentSessionId: string | null;
  isLoading: boolean;
}
```

#### 4.4 Modal Store (`client/src/components/stores/modalStore.ts`)
**File**: `client/src/components/stores/modalStore.ts`
**Purpose**: Global modal state management

#### 4.5 Toast Store (`client/src/components/stores/toastStore.ts`)
**File**: `client/src/components/stores/toastStore.ts`
**Purpose**: Toast notification system

### 5. Component Dependency Tree

#### 5.1 TopBar Component (`client/src/components/TopBar/TopBar.tsx`)
**Dependencies**:
- `useSettingsStore` (settingsStore.ts)
- `useModalStore` (modalStore.ts)
- `useChatStore` (chatStore.ts)
- `ProviderStatusIndicator` (ui/ProviderStatusIndicator.tsx)
- Lucide icons: Plus, History, Settings, ChevronDown, Loader2

#### 5.2 ChatMessages Component (`client/src/components/Chat/ChatMessages.tsx`)
**Dependencies**:
- `useChatStore` (chatStore.ts)
- `ChatMessage` (Chat/ChatMessage.tsx)
- `ArrowDown` (lucide-react)
- BrandLogo.svg (assets)

#### 5.3 InputArea Component (`client/src/components/Chat/InputArea.tsx`)
**Dependencies**:
- `useChatStore` (chatStore.ts)
- Lucide icons: Paperclip, Send, Mic, Loader2

#### 5.4 ModalRoot Component (`client/src/components/Modals/ModalRoot.tsx`)
**Dependencies**:
- `useModalStore` (modalStore.ts)
- ProviderModal (Modals/ProviderModal.tsx)
- SettingsModal (Modals/SettingsModal.tsx)
- ChatHistoryModal (Modals/ChatHistoryModal.tsx)
- StatusModal (Modals/StatusModal.tsx)

### 6. Provider System Architecture

#### 6.1 Provider Registry (`client/src/providers/index.ts`)
**File**: `client/src/providers/index.ts`
**Purpose**: Centralized provider component exports

**Available Providers** (15 total):
- OpenAI, Anthropic, Gemini, Groq, DeepSeek
- Mistral, Moonshot, OpenRouter, Perplexity, Qwen
- Together, Vertex, xAI, Ollama, LMStudio

#### 6.2 Provider Modal (`client/src/components/Modals/ProviderModal.tsx`)
**Dependencies**:
- `useSettingsStore` (settingsStore.ts)
- `useModalStore` (modalStore.ts)
- ProviderLogo (ui/ProviderLogo.tsx)
- SearchableModelSelect (ui/SearchableModelSelect.tsx)
- Lucide icons: Search, Loader2, ChevronDown

### 7. UI Component Dependencies

#### 7.1 SearchableModelSelect (`client/src/components/ui/SearchableModelSelect.tsx`)
**Dependencies**:
- Lucide icons: Search
- Custom dropdown implementation

#### 7.2 ProviderStatusIndicator (`client/src/components/ui/ProviderStatusIndicator.tsx`)
**Dependencies**:
- `useSettingsStore` (settingsStore.ts)
- Lucide icons: CheckCircle2, AlertCircle, XCircle, Loader2

#### 7.3 ShikiCodeBlock (`client/src/components/Chat/ShikiCodeBlock.tsx`)
**Dependencies**:
- `shiki` library
- `getOptimizedHighlighter` (utils/shiki-setup.ts)
- Lucide icons: Copy, Download

### 8. Utility Dependencies

#### 8.1 Shiki Setup (`client/src/utils/shiki-setup.ts`)
**Dependencies**:
- `shiki` library
- Essential languages: javascript, typescript, jsx, tsx, html, css, json, xml, yaml, markdown, python, shell, actionscript-3

### 9. Build & Configuration Files

#### 9.1 Vite Configuration (`vite.config.ts`)
**Purpose**: Build configuration for CEP extension
**Key Features**:
- React plugin
- Single-file output for CEP compatibility
- TypeScript support

#### 9.2 PostCSS Configuration (`postcss.config.js`)
**Purpose**: CSS processing with Tailwind CSS

#### 9.3 Tailwind Configuration (`tailwind.config.js`)
**Purpose**: Adobe-themed styling system

### 10. Execution Path Analysis

#### 10.1 Application Startup Flow
```
1. main.tsx → initializeCEP()
2. main.tsx → loadSettings()
3. main.tsx → initializeToastStore()
4. main.tsx → render App.tsx
5. App.tsx → mount TopBar, ChatMessages, InputArea, ModalRoot, ToastContainer
6. TopBar → loadSettings() → loadModelsForProvider()
7. ChatMessages → check currentSession
8. InputArea → initialize textarea
```

#### 10.2 Provider Configuration Flow
```
1. User clicks provider selector in TopBar
2. TopBar → openModal('provider')
3. ModalRoot → render ProviderModal
4. ProviderModal → load provider-specific component
5. Provider component → fetch models via ProviderBridge
6. User selects model → saveProviderSelection()
7. Settings persisted via CEPSettings
```

#### 10.3 Message Flow
```
1. User types in InputArea
2. InputArea → addMessage() (user message)
3. ChatStore → update messages
4. ChatMessages → re-render with new message
5. ChatStore → simulate AI response
6. ChatStore → addMessage() (assistant message)
7. HistoryStore → auto-save session
```

### 11. File Size & Performance Considerations

#### 11.1 Bundle Optimization
- **Shiki**: Limited to essential languages only
- **Icons**: Using Lucide React (tree-shakeable)
- **Providers**: Lazy-loaded provider components
- **CEP**: Single-file output via vite-plugin-singlefile

#### 11.2 CEP-Specific Optimizations
- **CSInterface**: Singleton pattern
- **ExtendScript**: Promise-based with retry logic
- **Settings**: Dual persistence (CEP + localStorage fallback)
- **Models**: Fallback lists for offline development

### 12. Security Considerations

#### 12.1 API Key Management
- Keys stored in CEP persistent storage
- Encrypted at rest (CEP environment)
- Never exposed in source code

#### 12.2 ExtendScript Security
- Input validation for all ExtendScript calls
- Timeout protection for long-running operations
- Error handling with retry logic

## Conclusion

The SahAI project demonstrates a well-architected React application designed specifically for Adobe CEP environments. The dependency tree shows clear separation of concerns with:

1. **Modular state management** using Zustand
2. **Provider-agnostic architecture** supporting 15 AI providers
3. **CEP-optimized build** with single-file output
4. **Robust error handling** with fallback mechanisms
5. **Performance-focused** component design with lazy loading

The execution paths are clearly defined from application startup through user interactions, with proper handling of both CEP and development environments.
