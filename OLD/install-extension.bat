@echo off
REM SahAI CEP Extension Installation Script for Windows
REM This script installs the extension in the correct CEP directory

echo 🔧 Installing SahAI CEP Extension...

REM CEP extension directories for Windows
set CEP_DIR=%APPDATA%\Adobe\CEP\extensions
set EXTENSION_NAME=com.sahai.cep
set EXTENSION_PATH=%CEP_DIR%\%EXTENSION_NAME%

REM Create CEP directory if it doesn't exist
if not exist "%CEP_DIR%" mkdir "%CEP_DIR%"

REM Remove existing extension if it exists
if exist "%EXTENSION_PATH%" (
    echo 🗑️  Removing existing extension...
    rmdir /s /q "%EXTENSION_PATH%"
)

REM Copy the extension
echo 📦 Copying extension files...
xcopy /s /e /h /y dist "%EXTENSION_PATH%\"

REM Enable debug mode for CEP
echo 🔍 Enabling CEP debug mode...
reg add "HKEY_CURRENT_USER\Software\Adobe\CSXS.11" /v PlayerDebugMode /t REG_SZ /d 1 /f
reg add "HKEY_CURRENT_USER\Software\Adobe\CSXS.10" /v PlayerDebugMode /t REG_SZ /d 1 /f
reg add "HKEY_CURRENT_USER\Software\Adobe\CSXS.9" /v PlayerDebugMode /t REG_SZ /d 1 /f

echo ✅ Extension installed successfully!
echo 📍 Location: %EXTENSION_PATH%
echo.
echo 🔄 Please restart After Effects
echo 📋 Look for 'SahAI Chat Bot' in Window > Extensions
echo.
echo 🔍 Debug logs will appear in After Effects' ExtendScript Toolkit console
pause
