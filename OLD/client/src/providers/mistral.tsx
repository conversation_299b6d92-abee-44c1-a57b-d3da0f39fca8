import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';
import { SearchableModelSelect } from '../components/ui/SearchableModelSelect';
import { ProviderBridge } from '../utils/cepIntegration';

export const MistralProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState('');

  useEffect(() => {
    ProviderBridge.listModels('mistral', 'https://api.mistral.ai/v1', key)
      .then((models: any) => setModels(models as { id: string; name: string }[]));
  }, [key]);

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  return (
    <div className="space-y-4">
      {/* Select Model Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <SearchableModelSelect
          models={models}
          value={selectedModel}
          onChange={handleModelChange}
          placeholder="Search Mistral models..."
        />
      </div>

      {/* Input API Key Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>

      {/* Save & Close Button */}
      <button
        onClick={() => { updateProviderKey('mistral', key, selectedModel); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
