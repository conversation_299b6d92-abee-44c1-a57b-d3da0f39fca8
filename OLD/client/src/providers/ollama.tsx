import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { ProviderBridge } from '../utils/cepIntegration';
import { Loader2 } from 'lucide-react';

const isMac = /Mac/.test(navigator.platform);
const defaultBaseURL = isMac ? 'http://localhost:11434' : 'http://localhost:11434';

interface Props {
  onSave: (config: { baseURL: string; selectedModelId: string }) => void;
  onClose: () => void;
}

export const OllamaProvider: React.FC<Props> = ({ onSave, onClose }) => {
  const { getActiveProvider } = useSettingsStore();
  const [baseURL, setBaseURL] = useState(getActiveProvider()?.baseURL || defaultBaseURL);
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState(getActiveProvider()?.selectedModelId || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!baseURL) return;

    setLoading(true);
    setError('');
    ProviderBridge.listModels('ollama', baseURL)
      .then((models: any) => {
        setModels(models as { id: string; name: string }[]);
        setError('');
      })
      .catch((e: any) => setError('Failed to load models: ' + e.message))
      .finally(() => setLoading(false));
  }, [baseURL]);

  return (
    <div className="space-y-4">
      {/* Base URL Section */}
      <div>
        <label className="block text-sm font-medium text-adobe-text-primary mb-2">
          Base URL
        </label>
        <input
          type="text"
          placeholder={defaultBaseURL}
          value={baseURL}
          onChange={(e) => setBaseURL(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
        />
      </div>

      {/* Model Selection */}
      <div>
        <label className="block text-sm font-medium text-adobe-text-primary mb-2">
          Model
        </label>
        {loading ? (
          <div className="flex items-center space-x-2 text-adobe-text-secondary">
            <Loader2 size={16} className="animate-spin" />
            <span>Loading models...</span>
          </div>
        ) : error ? (
          <div className="space-y-2">
            <p className="text-adobe-error text-sm">{error}</p>
            <button
              onClick={() => {
                setLoading(true);
                setError('');
                ProviderBridge.listModels('ollama', baseURL)
                  .then((models: any) => {
                    setModels(models as { id: string; name: string }[]);
                    setError('');
                  })
                  .catch((e: any) => setError('Failed to load models: ' + e.message))
                  .finally(() => setLoading(false));
              }}
              className="text-adobe-accent text-sm hover:underline"
            >
              Retry
            </button>
          </div>
        ) : models.length === 0 ? (
          <div className="space-y-2">
            <p className="text-adobe-warning text-sm">No models found. Check config or retry.</p>
            <p className="text-adobe-text-secondary text-xs">
              Make sure Ollama is running and accessible at the configured URL.
            </p>
            <button
              onClick={() => {
                setLoading(true);
                setError('');
                ProviderBridge.listModels('ollama', baseURL)
                  .then((models: any) => {
                    setModels(models as { id: string; name: string }[]);
                    setError('');
                  })
                  .catch((e: any) => setError('Failed to load models: ' + e.message))
                  .finally(() => setLoading(false));
              }}
              className="text-adobe-accent text-sm hover:underline"
            >
              Retry
            </button>
          </div>
        ) : (
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
          >
            <option value="">Select a model...</option>
            {models.map((m) => (
              <option key={m.id} value={m.id}>{m.name}</option>
            ))}
          </select>
        )}
      </div>

      {/* Save Button */}
      <button
        onClick={() => onSave({ baseURL, selectedModelId: selectedModel })}
        disabled={!baseURL || !selectedModel}
        className="w-full bg-adobe-accent text-white rounded-md py-3 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
      >
        Save Configuration
      </button>
    </div>
  );
};
