import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { initializeCEP } from './utils/cepIntegration';
import { useSettingsStore } from './stores/settingsStore';
import { useToastStore } from './components/stores/toastStore';

// Initialize CEP environment if running in CEP
initializeCEP();

// Load settings on app start
useSettingsStore.getState().loadSettings();

// Initialize toast store to ensure it's ready for global usage
// This ensures the store is properly initialized before any components try to use it
useToastStore.getState(); // This initializes the store if not already done

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
