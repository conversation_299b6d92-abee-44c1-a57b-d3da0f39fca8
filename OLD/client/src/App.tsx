// src/App.tsx - Final, post-refactor version
// ----------------------------------------------------------
// - Uses the new **clean InputArea** (no horizontal line glitch)
// - Uses the new **ChatMessages** component with Cline-style UI
// - Uses the new **TopBar** (model selector via modalStore)
// - Uses **CSXS-safe** import paths and Adobe-theme tokens

import React from 'react';

import TopBar          from './components/TopBar/TopBar';
import ChatMessages    from './components/Chat/ChatMessages';
import InputArea       from './components/Chat/InputArea';
import { ModalRoot }   from './components/Modals/ModalRoot';
import { ToastContainer } from './components/ui/Toast';

const App: React.FC = () => (
  <div className="flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans">
    {/* 1. Top navigation: provider status + model selector + actions */}
    <TopBar />

    {/* 2. Scrollable message thread with Cline-style bubbles & Shiki blocks */}
    <ChatMessages />

    {/* 3. Typing box: Lucide icons inside transparent rounded container */}
    <InputArea />

    {/* 4. Global modal layer (provider, settings, history, etc.) */}
    <ModalRoot />

    {/* 5. Toast notifications for user feedback */}
    <ToastContainer />
  </div>
);

export default App;
