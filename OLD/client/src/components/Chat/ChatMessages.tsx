import React, { useEffect, useRef, useState } from 'react';
import { useChatStore } from '../stores/chatStore';
import { ChatMessage } from './ChatMessage';
import { ArrowDown } from 'lucide-react';
import BrandLogo from '../../../../images/BrandLogo.svg';

export const ChatMessages: React.FC = () => {
  const { messages, isLoading, currentSession } = useChatStore();
  const bottomRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout>>();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  // Show/hide scroll-to-bottom button based on scroll position
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      clearTimeout(scrollTimeoutRef.current);
      
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - (scrollTop + clientHeight) < 100;
      
      setShowScrollButton(!isNearBottom);

      // Hide button after 2 seconds of inactivity
      scrollTimeoutRef.current = setTimeout(() => {
        setShowScrollButton(false);
      }, 2000);
    };

    container.addEventListener('scroll', handleScroll);
    return () => {
      container.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeoutRef.current);
    };
  }, []);

  const scrollToBottom = () => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative"
    >
      {(!currentSession || messages.length === 0) && (
        <div className="flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3">
          <div className="w-20 h-20 mb-2 flex items-center justify-center">
            <img
              src={BrandLogo}
              alt="SahAI Logo"
              className="w-20 h-20 brightness-0 invert"
            />
          </div>
          <h3 className="text-lg font-medium text-adobe-text-primary">Start a conversation</h3>
          <p className="text-sm text-center max-w-md">Type a message below to begin chatting with SahAI</p>
        </div>
      )}

      {messages.map((msg) => (
        <ChatMessage key={msg.id} message={msg} />
      ))}

      {isLoading && (
        <div className="flex items-center gap-2 text-adobe-text-secondary text-sm">
          <span>AI is thinking...</span>
        </div>
      )}

      <div ref={bottomRef} />

      {showScrollButton && (
        <button
          onClick={scrollToBottom}
          className="absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md"
          aria-label="Scroll to bottom"
        >
          <ArrowDown size={18} />
        </button>
      )}
    </div>
  );
};

export default ChatMessages;