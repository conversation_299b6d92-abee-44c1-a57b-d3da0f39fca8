import React, { useEffect, useState } from 'react';
import { isCEPEnvironment, getAppInfo } from '../utils/cepIntegration';

const CEPStatus: React.FC = () => {
  const [isCEP, setIsCEP] = useState(false);
  const [appInfo, setAppInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkCEPStatus = async () => {
      const cepEnv = isCEPEnvironment();
      setIsCEP(cepEnv);
      
      if (cepEnv) {
        try {
          const appData = await getAppInfo();
          setAppInfo(appData?.data || appData);
        } catch (error) {
          console.error('Failed to get CEP info:', error);
          setAppInfo(null);
        }
      }
      
      setLoading(false);
    };

    checkCEPStatus();
  }, []);

  if (loading) {
    return (
      <div className="p-2 text-xs text-adobe-text-secondary">
        Checking CEP environment...
      </div>
    );
  }

  if (!isCEP) {
    return (
      <div className="p-2 text-xs text-adobe-text-secondary">
        Running in browser mode
      </div>
    );
  }

  return (
    <div className="p-2 text-xs text-adobe-text-secondary">
      {appInfo && (
        <div>
          Host: {appInfo.name} {appInfo.version}
        </div>
      )}
    </div>
  );
};

export default CEPStatus;
