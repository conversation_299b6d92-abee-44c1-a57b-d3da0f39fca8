import { renderHook, act } from '@testing-library/react';
import { useHistoryStore } from './historyStore';
import { executeExtendScript } from '../../utils/cepIntegration';

// Mock the CEP integration
jest.mock('../../utils/cepIntegration');
const mockExecuteExtendScript = executeExtendScript as jest.MockedFunction<typeof executeExtendScript>;

// Mock CSInterface
const mockCSInterface = {
  evalScript: jest.fn(),
};

Object.defineProperty(window, 'CSInterface', {
  value: mockCSInterface,
  writable: true,
});

describe('useHistoryStore', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    
    // Reset the store
    useHistoryStore.setState({
      sessions: [],
      currentSessionId: null,
      isLoading: false,
      error: null,
    });
  });

  describe('loadHistory', () => {
    it('loads history from ExtendScript successfully', async () => {
      const mockSessions = [
        {
          id: '1',
          title: 'Test Chat',
          messages: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      ];

      mockExecuteExtendScript.mockResolvedValue({
        success: true,
        data: mockSessions,
      });

      const { result } = renderHook(() => useHistoryStore());

      await act(async () => {
        await result.current.loadHistory();
      });

      expect(result.current.sessions).toEqual(mockSessions);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('falls back to localStorage when ExtendScript fails', async () => {
      const mockSessions = [
        {
          id: '1',
          title: 'Local Chat',
          messages: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      ];

      localStorage.setItem('sahai-chat-history', JSON.stringify(mockSessions));
      mockExecuteExtendScript.mockRejectedValue(new Error('ExtendScript failed'));

      const { result } = renderHook(() => useHistoryStore());

      await act(async () => {
        await result.current.loadHistory();
      });

      expect(result.current.sessions).toEqual(mockSessions);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toContain('Using local storage fallback');
    });

    it('works without CEP environment', async () => {
      // Remove CSInterface to simulate non-CEP environment
      delete (window as any).CSInterface;

      const mockSessions = [
        {
          id: '1',
          title: 'Browser Chat',
          messages: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      ];

      localStorage.setItem('sahai-chat-history', JSON.stringify(mockSessions));

      const { result } = renderHook(() => useHistoryStore());

      await act(async () => {
        await result.current.loadHistory();
      });

      expect(result.current.sessions).toEqual(mockSessions);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });
  });

  describe('saveSession', () => {
    it('saves session to both ExtendScript and localStorage', async () => {
      const mockSession = {
        id: '1',
        title: 'Test Chat',
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mockExecuteExtendScript.mockResolvedValue({ success: true });

      const { result } = renderHook(() => useHistoryStore());

      await act(async () => {
        await result.current.saveSession(mockSession);
      });

      expect(result.current.sessions).toContainEqual(mockSession);
      expect(localStorage.getItem('sahai-chat-history')).toContain(mockSession.id);
    });

    it('falls back to localStorage when ExtendScript fails', async () => {
      const mockSession = {
        id: '1',
        title: 'Test Chat',
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mockExecuteExtendScript.mockRejectedValue(new Error('ExtendScript failed'));

      const { result } = renderHook(() => useHistoryStore());

      await act(async () => {
        await result.current.saveSession(mockSession);
      });

      expect(result.current.sessions).toContainEqual(mockSession);
      expect(localStorage.getItem('sahai-chat-history')).toContain(mockSession.id);
    });
  });

  describe('createSession', () => {
    it('creates a new session and saves it', async () => {
      mockExecuteExtendScript.mockResolvedValue({ success: true });

      const { result } = renderHook(() => useHistoryStore());

      let newSession;
      await act(async () => {
        newSession = result.current.createSession('Test Title');
      });

      expect(newSession).toBeDefined();
      expect(newSession?.title).toBe('Test Title');
      expect(result.current.sessions).toHaveLength(1);
      expect(result.current.currentSessionId).toBe(newSession?.id);
    });
  });

  describe('deleteSession', () => {
    it('deletes a session and updates storage', async () => {
      const mockSession = {
        id: '1',
        title: 'Test Chat',
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mockExecuteExtendScript.mockResolvedValue({ success: true });

      const { result } = renderHook(() => useHistoryStore());

      // First add a session
      await act(async () => {
        await result.current.saveSession(mockSession);
      });

      expect(result.current.sessions).toHaveLength(1);

      // Then delete it
      await act(async () => {
        await result.current.deleteSession('1');
      });

      expect(result.current.sessions).toHaveLength(0);
    });
  });
});
