import React, { useState } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { X } from 'lucide-react';
import { OllamaProvider } from '../../providers/ollama';
import { LMStudioProvider } from '../../providers/lmstudio';
import { OpenAIProvider } from '../../providers/openai';
import { AnthropicProvider } from '../../providers/anthropic';
import { GeminiProvider } from '../../providers/gemini';
import { GroqProvider } from '../../providers/groq';
import { DeepSeekProvider } from '../../providers/deepseek';
import { MistralProvider } from '../../providers/mistral';
import { MoonshotProvider } from '../../providers/moonshot';
import { OpenRouterProvider } from '../../providers/openrouter';
import { PerplexityProvider } from '../../providers/perplexity';
import { QwenProvider } from '../../providers/qwen';
import { TogetherProvider } from '../../providers/together';
import { VertexProvider } from '../../providers/vertex';
import { XAIProvider } from '../../providers/xai';

export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { activeProviderId, updateProviderKey, saveProviderSelection } = useSettingsStore();
  const [selectedProvider, setSelectedProvider] = useState(activeProviderId || '');

  const handleSave = (providerId: string, config: { apiKey?: string; baseURL?: string; selectedModelId?: string }) => {
    if (config.apiKey) {
      updateProviderKey(providerId, config.apiKey, config.selectedModelId);
    } else if (config.baseURL) {
      saveProviderSelection(providerId, { baseURL: config.baseURL, selectedModelId: config.selectedModelId });
    }
    closeModal();
  };

  const renderProviderConfig = () => {
    if (!selectedProvider) return null;

    const props = {
      onSave: (config: any) => handleSave(selectedProvider, config),
      onClose: closeModal
    };

    switch (selectedProvider) {
      case 'ollama':
        return <OllamaProvider {...props} />;
      case 'lmstudio':
        return <LMStudioProvider {...props} />;
      case 'openai':
        return <OpenAIProvider {...props} />;
      case 'anthropic':
        return <AnthropicProvider {...props} />;
      case 'gemini':
        return <GeminiProvider {...props} />;
      case 'groq':
        return <GroqProvider {...props} />;
      case 'deepseek':
        return <DeepSeekProvider {...props} />;
      case 'mistral':
        return <MistralProvider {...props} />;
      case 'moonshot':
        return <MoonshotProvider {...props} />;
      case 'openrouter':
        return <OpenRouterProvider {...props} />;
      case 'perplexity':
        return <PerplexityProvider {...props} />;
      case 'qwen':
        return <QwenProvider {...props} />;
      case 'together':
        return <TogetherProvider {...props} />;
      case 'vertex':
        return <VertexProvider {...props} />;
      case 'xai':
        return <XAIProvider {...props} />;
      default:
        return null;
    }
  };

  const providers = [
    { id: 'openai', name: 'OpenAI', type: 'apiKey' },
    { id: 'anthropic', name: 'Anthropic', type: 'apiKey' },
    { id: 'gemini', name: 'Google Gemini', type: 'apiKey' },
    { id: 'groq', name: 'Groq', type: 'apiKey' },
    { id: 'deepseek', name: 'DeepSeek', type: 'apiKey' },
    { id: 'mistral', name: 'Mistral', type: 'apiKey' },
    { id: 'moonshot', name: 'Moonshot AI', type: 'apiKey' },
    { id: 'openrouter', name: 'OpenRouter', type: 'apiKey' },
    { id: 'perplexity', name: 'Perplexity', type: 'apiKey' },
    { id: 'qwen', name: 'Alibaba Qwen', type: 'apiKey' },
    { id: 'together', name: 'Together AI', type: 'apiKey' },
    { id: 'vertex', name: 'Google Vertex AI', type: 'apiKey' },
    { id: 'xai', name: 'xAI', type: 'apiKey' },
    { id: 'ollama', name: 'Ollama', type: 'baseURL' },
    { id: 'lmstudio', name: 'LM Studio', type: 'baseURL' },
  ];

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] max-h-[600px] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Configure Provider
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {!selectedProvider ? (
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Select a Provider</h3>
              <div className="space-y-2">
                {providers.map((provider) => (
                  <button
                    key={provider.id}
                    onClick={() => setSelectedProvider(provider.id)}
                    className="w-full text-left p-3 rounded-md bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary transition-colors"
                  >
                    <div className="font-medium text-adobe-text-primary">{provider.name}</div>
                    <div className="text-xs text-adobe-text-secondary mt-1">
                      {provider.type === 'apiKey' ? 'API Key required' : 'Base URL required'}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : (
            <div>
              <button
                onClick={() => setSelectedProvider('')}
                className="text-sm text-adobe-accent hover:underline mb-4"
              >
                ← Back to providers
              </button>
              {renderProviderConfig()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
