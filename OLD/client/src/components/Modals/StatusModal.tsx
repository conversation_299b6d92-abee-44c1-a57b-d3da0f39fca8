import React, { useEffect, useState } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useSettingsStore } from '../stores/settingsStore';
import { ProviderStatusChecker } from '../../utils/cepIntegration';
import { X, Wifi, WifiOff, Loader2, AlertCircle, RefreshCw, ChevronDown } from 'lucide-react';

interface StatusDetails {
  isOnline: boolean | null;
  latency?: number;
  isChecking: boolean;
  error?: string;
  lastChecked?: number;
}

export const StatusModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { getActiveProvider, providers } = useSettingsStore();
  const [status, setStatus] = useState<StatusDetails>({
    isOnline: null,
    isChecking: false,
  });

  const activeProvider = getActiveProvider();

  const checkStatus = async (force = false) => {
    if (!activeProvider?.isConfigured) {
      setStatus({ isOnline: null, isChecking: false });
      return;
    }

    setStatus(prev => ({ ...prev, isChecking: true, error: undefined }));
    try {
      const result = await ProviderStatusChecker.checkProviderStatus(
        activeProvider.id,
        { apiKey: activeProvider.apiKey, baseURL: activeProvider.baseURL }
      );
      setStatus({
        isOnline: result.isOnline,
        latency: result.latency,
        isChecking: false,
        lastChecked: Date.now(),
      });
    } catch (error: any) {
      setStatus({
        isOnline: false,
        isChecking: false,
        error: error.message,
        lastChecked: Date.now(),
      });
    }
  };

  useEffect(() => {
    checkStatus();
    const interval = setInterval(checkStatus, 30000);
    return () => clearInterval(interval);
  }, [activeProvider]);

  const getStatusIcon = () => {
    if (status.isChecking) return <Loader2 size={20} className="animate-spin text-yellow-500" />;
    if (status.isOnline === true) return <Wifi size={20} className="text-green-500" />;
    if (status.isOnline === false) return <WifiOff size={20} className="text-red-500" />;
    return <AlertCircle size={20} className="text-gray-500" />;
  };

  const getStatusText = () => {
    if (status.isChecking) return 'Checking connection...';
    if (status.isOnline === true) return 'Online';
    if (status.isOnline === false) return 'Offline';
    return 'Unknown';
  };

  const getStatusColor = () => {
    if (status.isChecking) return 'text-yellow-600';
    if (status.isOnline === true) return 'text-green-600';
    if (status.isOnline === false) return 'text-red-600';
    return 'text-gray-600';
  };

  const getStatusClass = () => {
    if (status.isOnline === true) return 'good';
    if (status.isOnline === false) return 'critical';
    return 'warning';
  };

  const getStatusTooltip = () => {
    if (status.isChecking) return 'Checking provider status...';
    if (status.isOnline === true) return 'Provider is online and responding';
    if (status.isOnline === false) return 'Provider is offline or not responding';
    return 'Provider status unknown';
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Provider Status
            </h2>
            <div className="flex items-center gap-3">
              <button
                onClick={() => checkStatus(true)}
                disabled={status.isChecking}
                className="p-1 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded transition-colors disabled:opacity-50"
                title="Refresh status"
              >
                <RefreshCw size={18} className={status.isChecking ? 'animate-spin' : ''} />
              </button>
              <button
                onClick={closeModal}
                className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
              >
                <X size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden p-4">
          {!activeProvider ? (
            <div className="flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2">
              <AlertCircle size={48} className="opacity-50" />
              <h3 className="text-lg font-medium text-adobe-text-primary">
                No provider selected
              </h3>
              <p className="text-sm">
                Select a provider to check connection status
              </p>
            </div>
          ) : (
            <div className="h-full flex flex-col gap-4">
              {/* Provider Info Card */}
              <div className="bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      {getStatusIcon()}
                    </div>
                    <div>
                      <h3 className="font-medium text-adobe-text-primary">{activeProvider.name}</h3>
                      <p className={`text-sm font-medium ${getStatusColor()}`}>
                        {getStatusText()}
                      </p>
                    </div>
                  </div>
                  <div className={`text-xs px-2 py-1 rounded ${getStatusClass() === 'good' ? 'bg-green-900/30 text-green-500' : getStatusClass() === 'warning' ? 'bg-yellow-900/30 text-yellow-500' : 'bg-red-900/30 text-red-500'}`}>
                    {getStatusText()}
                  </div>
                </div>
              </div>

              {/* Status Grid */}
              <div className="grid grid-cols-2 gap-4">
                {/* Latency Card */}
                <div className="bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-adobe-text-secondary">Latency</span>
                    <span className="text-xs text-adobe-text-tertiary">Lower is better</span>
                  </div>
                  <div className="mt-2">
                    {status.latency ? (
                      <div className="flex items-end gap-2">
                        <span className="text-2xl font-medium text-adobe-text-primary">
                          {status.latency}ms
                        </span>
                        <span className={`text-xs mb-1 ${status.latency < 100 ? 'text-green-500' : status.latency < 300 ? 'text-yellow-500' : 'text-red-500'}`}>
                          {status.latency < 100 ? 'Excellent' : status.latency < 300 ? 'Good' : 'Poor'}
                        </span>
                      </div>
                    ) : (
                      <span className="text-adobe-text-tertiary">--</span>
                    )}
                  </div>
                </div>

                {/* Last Checked Card */}
                <div className="bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border">
                  <div className="text-sm text-adobe-text-secondary">Last Checked</div>
                  <div className="mt-2">
                    {status.lastChecked ? (
                      <div className="text-adobe-text-primary">
                        <div className="text-xl font-medium">
                          {new Date(status.lastChecked).toLocaleTimeString()}
                        </div>
                        <div className="text-xs text-adobe-text-tertiary mt-1">
                          {new Date(status.lastChecked).toLocaleDateString()}
                        </div>
                      </div>
                    ) : (
                      <span className="text-adobe-text-tertiary">--</span>
                    )}
                  </div>
                </div>

                {/* Endpoint Card */}
                <div className="bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border col-span-2">
                  <div className="text-sm text-adobe-text-secondary">Endpoint</div>
                  <div className="mt-2">
                    {activeProvider.baseURL ? (
                      <div className="flex items-center justify-between">
                        <div className="truncate text-adobe-text-primary font-mono text-sm">
                          {activeProvider.baseURL}
                        </div>
                        <button 
                          className="text-xs text-adobe-accent hover:text-adobe-accent-hover"
                          onClick={() => navigator.clipboard.writeText(activeProvider.baseURL || '')}
                        >
                          Copy
                        </button>
                      </div>
                    ) : (
                      <span className="text-adobe-text-tertiary">Not configured</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Status Details */}
              <div className="flex-1 bg-adobe-bg-secondary p-4 rounded-lg border border-adobe-border overflow-auto">
                <div className="text-sm text-adobe-text-secondary mb-2">Status Details</div>
                {status.error ? (
                  <div className="p-3 bg-red-900/20 border border-red-800/50 rounded text-sm text-red-400">
                    <div className="font-medium mb-1">Error:</div>
                    <div>{status.error}</div>
                  </div>
                ) : (
                  <div className="text-sm text-adobe-text-primary">
                    {status.isChecking ? (
                      'Checking provider status...'
                    ) : status.isOnline === true ? (
                      'Provider is online and responding normally.'
                    ) : status.isOnline === false ? (
                      'Provider is offline or not responding to requests.'
                    ) : (
                      'Provider status unknown. Please check configuration.'
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-center bg-adobe-bg-secondary">
          Status checks are performed automatically every 30 seconds
        </div>
      </div>
    </div>
  );
};