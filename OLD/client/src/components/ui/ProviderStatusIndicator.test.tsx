import React from 'react';
import { render, screen } from '@testing-library/react';
import { ProviderStatusIndicator } from './ProviderStatusIndicator';
import { useSettingsStore } from '../stores/settingsStore';
import { ProviderStatusChecker } from '../../utils/cepIntegration';

// Mock the stores and utilities
jest.mock('../stores/settingsStore');
jest.mock('../../utils/cepIntegration');

const mockGetActiveProvider = jest.fn();
const mockUseSettingsStore = useSettingsStore as jest.MockedFunction<typeof useSettingsStore>;
const mockProviderStatusChecker = ProviderStatusChecker as jest.Mocked<typeof ProviderStatusChecker>;

describe('ProviderStatusIndicator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseSettingsStore.mockReturnValue({
      getActiveProvider: mockGetActiveProvider,
      providers: [],
      getActiveModel: jest.fn(),
      loadSettings: jest.fn(),
      saveSettings: jest.fn(),
      updateProvider: jest.fn(),
      deleteProvider: jest.fn(),
      addProvider: jest.fn(),
    });
  });

  it('renders with no provider selected', () => {
    mockGetActiveProvider.mockReturnValue(null);

    render(<ProviderStatusIndicator />);

    const indicator = screen.getByRole('generic');
    expect(indicator).toBeInTheDocument();
    expect(indicator).toHaveClass('bg-adobe-error');
  });

  it('renders online status correctly', async () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      isConfigured: true,
      apiKey: 'test-key',
      baseURL: 'https://api.test.com',
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockProviderStatusChecker.checkProviderStatus.mockResolvedValue({
      isOnline: true,
      latency: 150,
    });

    render(<ProviderStatusIndicator />);

    const indicator = screen.getByRole('generic');
    expect(indicator).toBeInTheDocument();
    
    // Initially should show checking state
    expect(indicator).toHaveClass('bg-adobe-warning', 'animate-pulse');
  });

  it('renders offline status correctly', async () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      isConfigured: true,
      apiKey: 'test-key',
      baseURL: 'https://api.test.com',
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);
    mockProviderStatusChecker.checkProviderStatus.mockRejectedValue(
      new Error('Connection failed')
    );

    render(<ProviderStatusIndicator />);

    const indicator = screen.getByRole('generic');
    expect(indicator).toBeInTheDocument();
  });

  it('renders clickable indicator without tooltip', () => {
    const mockProvider = {
      id: 'test-provider',
      name: 'Test Provider',
      isConfigured: true,
    };

    mockGetActiveProvider.mockReturnValue(mockProvider);

    render(<ProviderStatusIndicator />);

    const indicator = screen.getByRole('generic');
    expect(indicator).not.toHaveAttribute('title');
    expect(indicator).toHaveClass('cursor-pointer');
  });

  it('has proper styling classes', () => {
    mockGetActiveProvider.mockReturnValue(null);

    render(<ProviderStatusIndicator />);

    const indicator = screen.getByRole('generic');
    expect(indicator).toHaveClass(
      'w-2.5',
      'h-2.5',
      'rounded-full',
      'transition-all',
      'duration-300',
      'shadow-sm',
      'hover:scale-110',
      'cursor-pointer'
    );
  });
});
