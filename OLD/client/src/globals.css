@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@400;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --adobe-bg-primary: #2b2b2b;
  --adobe-bg-secondary: #323232;
  --adobe-bg-tertiary: #3c3c3c;
  --adobe-text-primary: #ffffff;
  --adobe-text-secondary: #b7b7b7;
  --adobe-border: #4a4a4a;
  --adobe-accent: #1473e6;
  --adobe-accent-hover: #0f62d0;
  --adobe-error: #ff4d4d;
  --adobe-warning: #ffba00;
  --adobe-success: #2ecc71;
  --radius-md: 4px;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 13px;
  --font-size-lg: 14px;
  --font-size-xl: 16px;
  --font-size-2xl: 18px;
  --font-size-3xl: 22px;
}

/* Custom scrollbar styles for CEP dark theme */
::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

::-webkit-scrollbar-track {
  background: var(--adobe-bg-secondary);
  border-radius: 7px;
}

::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 7px;
  border: 2px solid var(--adobe-bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

::-webkit-scrollbar-thumb:active {
  background: #6a6a6a;
}

/* Firefox scrollbar styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--adobe-border) var(--adobe-bg-secondary);
}

/* Custom scrollbar for chat messages container */
.chat-messages-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.chat-messages-scrollbar::-webkit-scrollbar-track {
  background: rgba(50, 50, 50, 0.3);
  border-radius: 6px;
}

.chat-messages-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(74, 74, 74, 0.6);
  border-radius: 6px;
  border: none;
}

.chat-messages-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(90, 90, 90, 0.8);
}

.chat-messages-scrollbar::-webkit-scrollbar-thumb:active {
  background: rgba(100, 100, 100, 0.9);
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Source Sans 3', sans-serif;
  font-size: var(--font-size-base);
  height: 100vh;
  overflow: hidden;
}

/* Auto-resizing textarea */
.auto-resize-textarea {
  min-height: 72px;
  max-height: 200px;
  height: var(--textarea-height, 72px);
  transition: height 0.1s ease;
}
