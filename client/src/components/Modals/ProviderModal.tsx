import React, { useState, useEffect } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { X, Search } from 'lucide-react';
import { SearchableModelSelect } from '../ui/SearchableModelSelect';
import { ProviderLogo } from '../ui/ProviderLogo';
import { ErrorBoundary } from '../ErrorBoundary';

export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { providers, activeProviderId, saveProviderSelection, loadModelsForProvider } = useSettingsStore();

  // State for the unified modal
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProviderId, setSelectedProviderId] = useState(activeProviderId || '');
  const [selectedModelId, setSelectedModelId] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [baseURL, setBaseURL] = useState('');

  // Get the selected provider
  const selectedProvider = providers.find(p => p.id === selectedProviderId);

  // Load models when provider is selected and configured
  useEffect(() => {
    if (selectedProvider && selectedProvider.isConfigured && selectedProvider.models.length === 0) {
      loadModelsForProvider(selectedProvider.id);
    }
  }, [selectedProvider, loadModelsForProvider]);

  // Update form fields when provider changes
  useEffect(() => {
    if (selectedProvider) {
      setApiKey(selectedProvider.apiKey || '');
      setBaseURL(selectedProvider.baseURL || '');
      setSelectedModelId(selectedProvider.selectedModelId || '');
    }
  }, [selectedProvider]);

  const handleSave = () => {
    if (!selectedProviderId) return;

    const config: { apiKey?: string; baseURL?: string; selectedModelId?: string } = {
      selectedModelId: selectedModelId
    };

    if (selectedProvider?.configType === 'apiKey') {
      config.apiKey = apiKey;
    } else if (selectedProvider?.configType === 'baseURL') {
      config.baseURL = baseURL;
    }

    saveProviderSelection(selectedProviderId, config);
    closeModal();
  };

  // Filter providers based on search query
  const filteredProviders = providers.filter(provider =>
    provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    provider.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get available models for the selected provider
  const availableModels = selectedProvider?.models.map(model => ({
    id: model.id,
    name: model.name
  })) || [];

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
        <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[750px] max-h-[700px] shadow-2xl flex flex-col">
          {/* Header */}
          <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-adobe-text-primary">
                Configure Provider
              </h2>
              <button
                onClick={closeModal}
                className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto space-y-6">
            {/* Provider Search */}
            <div>
              <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                Search Providers
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search providers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10"
                />
                <Search size={18} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary" />
              </div>
            </div>

            {/* Provider Selection */}
            <div>
              <div className="flex items-center gap-2 mb-4">
                <label className="text-sm font-semibold text-adobe-text-primary">
                  Select a Provider
                </label>
                <span className="text-adobe-accent text-lg">*</span>
                <span className="text-xs text-adobe-text-secondary">
                  API Key Required
                </span>
              </div>
              <div className="grid grid-cols-5 gap-3 max-h-64 overflow-y-auto">
                {filteredProviders.map((provider) => (
                  <button
                    key={provider.id}
                    onClick={() => setSelectedProviderId(provider.id)}
                    className={`relative p-4 rounded-lg transition-all duration-200 flex flex-col items-center space-y-2 min-h-[100px] ${
                      selectedProviderId === provider.id
                        ? 'bg-adobe-accent text-white shadow-lg scale-105'
                        : 'bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary text-adobe-text-primary hover:shadow-md'
                    }`}
                  >
                    {/* Provider Logo */}
                    <ProviderLogo provider={provider.id} size={24} />

                    {/* Provider Name with superscript */}
                    <div className="text-center">
                      <div className="text-sm font-medium leading-tight">
                        {provider.name}
                        {provider.configType === 'apiKey' && (
                          <sup className="text-adobe-accent ml-1">*</sup>
                        )}
                      </div>
                    </div>

                    {/* Configuration Status Indicator */}
                    {provider.isConfigured && (
                      <div className={`absolute top-1 right-1 w-2 h-2 rounded-full ${
                        selectedProviderId === provider.id ? 'bg-white' : 'bg-green-500'
                      }`} />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Model Selection - Only show if provider is selected */}
            {selectedProvider && (
              <div>
                <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                  Select Model
                </label>
                {selectedProvider.isLoading ? (
                  <div className="flex items-center space-x-2 text-adobe-text-secondary p-3 bg-adobe-bg-secondary rounded">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-adobe-accent"></div>
                    <span>Loading models...</span>
                  </div>
                ) : selectedProvider.error ? (
                  <div className="p-3 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-sm">
                    {selectedProvider.error}
                  </div>
                ) : availableModels.length > 0 ? (
                  <SearchableModelSelect
                    models={availableModels}
                    value={selectedModelId}
                    onChange={setSelectedModelId}
                    placeholder={`Search ${selectedProvider.name} models...`}
                  />
                ) : (
                  <div className="p-3 bg-adobe-bg-secondary rounded text-adobe-text-secondary text-sm">
                    No models available. Configure the provider first.
                  </div>
                )}
              </div>
            )}

            {/* API Key / Base URL Input - Only show if provider is selected */}
            {selectedProvider && (
              <div>
                <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
                  {selectedProvider.configType === 'apiKey' ? 'API Key' : 'Base URL'}
                </label>
                <input
                  type={selectedProvider.configType === 'apiKey' ? 'password' : 'text'}
                  placeholder={
                    selectedProvider.configType === 'apiKey'
                      ? `Enter your ${selectedProvider.name} API key...`
                      : `Enter ${selectedProvider.name} base URL...`
                  }
                  value={selectedProvider.configType === 'apiKey' ? apiKey : baseURL}
                  onChange={(e) => {
                    if (selectedProvider.configType === 'apiKey') {
                      setApiKey(e.target.value);
                    } else {
                      setBaseURL(e.target.value);
                    }
                  }}
                  className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none"
                />
              </div>
            )}

            {/* Save Button - Only show if provider is selected */}
            {selectedProvider && (
              <div className="flex justify-end space-x-3 pt-4 border-t border-adobe-border">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={
                    !selectedProviderId ||
                    (selectedProvider.configType === 'apiKey' && !apiKey) ||
                    (selectedProvider.configType === 'baseURL' && !baseURL)
                  }
                  className="px-4 py-2 bg-adobe-accent text-white rounded hover:bg-adobe-accent/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Save & Configure
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};
