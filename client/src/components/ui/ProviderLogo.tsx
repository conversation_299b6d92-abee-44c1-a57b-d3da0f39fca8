import React from 'react';

interface ProviderLogoProps extends React.SVGProps<SVGSVGElement> {
  provider: string;
  size?: number;
}

export const ProviderLogo: React.FC<ProviderLogoProps> = ({ 
  provider, 
  size = 16, 
  className = '', 
  ...props 
}) => {
  const baseProps = {
    width: size,
    height: size,
    viewBox: "0 0 24 24",
    fill: "currentColor",
    className: `provider-logo ${className}`,
    ...props
  };

  switch (provider) {
    case 'openai':
      return (
        <svg {...baseProps}>
          <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"/>
        </svg>
      );

    case 'anthropic':
      return (
        <svg {...baseProps}>
          <path d="M12 2l-1.5 3.5L8 8h2.5L12 6l1.5 2H16l-2.5-2.5L12 2zm0 5l-4 9h1.5l1-2.5h5l1 2.5H18l-4-9h-2zm-1.5 5.5L12 10l1.5 2.5h-3z"/>
        </svg>
      );

    case 'google':
    case 'gemini':
    case 'vertex':
      return (
        <svg {...baseProps}>
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      );

    case 'groq':
      return (
        <svg {...baseProps}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 14.5v-13c3.84.48 6.5 3.79 6 7.72-.46 3.6-3.28 6.28-6 5.28z"/>
        </svg>
      );

    case 'deepseek':
      return (
        <svg {...baseProps}>
          <path d="M12 2l8 6v8l-8 6-8-6V8l8-6zm0 2.5L6.5 8.5v7L12 19.5l5.5-4v-7L12 4.5zm0 2L16 9v6l-4 3-4-3V9l4-2.5z"/>
        </svg>
      );

    case 'mistral':
      return (
        <svg {...baseProps}>
          <path d="M3 3v18h18V3H3zm2 2h14v14H5V5zm2 2v10h10V7H7zm2 2h6v6H9V9z"/>
        </svg>
      );

    case 'moonshot':
      return (
        <svg {...baseProps}>
          <path d="M12 2l-2 6-6 2 6 2 2 6 2-6 6-2-6-2-2-6zm0 4l1 3 3 1-3 1-1 3-1-3-3-1 3-1 1-3z"/>
        </svg>
      );

    case 'openrouter':
      return (
        <svg {...baseProps}>
          <path d="M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41zm0 2.59L8 12l4 4 4-4-4-4z"/>
        </svg>
      );

    case 'perplexity':
      return (
        <svg {...baseProps}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3l3 3h-2v4h2l-3 3-3-3h2v-4H9l3-3z"/>
        </svg>
      );

    case 'qwen':
      return (
        <svg {...baseProps}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6z"/>
        </svg>
      );

    case 'together':
      return (
        <svg {...baseProps}>
          <path d="M8 4c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8 0c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zM8 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8 0c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/>
        </svg>
      );

    case 'xai':
      return (
        <svg {...baseProps}>
          <path d="M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"/>
        </svg>
      );

    case 'ollama':
      return (
        <svg {...baseProps}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.48.41-2.86 1.12-4.06L8 10.82V16h8v-5.18l2.88-2.88C19.59 9.14 20 10.52 20 12c0 4.41-3.59 8-8 8z"/>
        </svg>
      );

    case 'lmstudio':
      return (
        <svg {...baseProps}>
          <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5zm2-8h-2v-2h2v2z"/>
        </svg>
      );

    case 'cohere':
      return (
        <svg {...baseProps}>
          <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"/>
        </svg>
      );

    case 'huggingface':
      return (
        <svg {...baseProps}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      );

    case 'azure':
      return (
        <svg {...baseProps}>
          <path d="M3 3v18l9-3 9 3V3H3zm2 2h14v12l-7-2.33L5 17V5z"/>
        </svg>
      );

    case 'claude':
      return (
        <svg {...baseProps}>
          <path d="M12 2l-1.5 3.5L8 8h2.5L12 6l1.5 2H16l-2.5-2.5L12 2zm0 5l-4 9h1.5l1-2.5h5l1 2.5H18l-4-9h-2zm-1.5 5.5L12 10l1.5 2.5h-3z"/>
        </svg>
      );

    case 'bedrock':
      return (
        <svg {...baseProps}>
          <path d="M3 3v18l9-3 9 3V3H3zm2 2h14v12l-7-2.33L5 17V5zm7-2l3 3h-2v4h2l-3 3-3-3h2v-4H9l3-3z"/>
        </svg>
      );

    default:
      // Generic fallback icon
      return (
        <svg {...baseProps}>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      );
  }
};