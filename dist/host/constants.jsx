/**
 * Constants for SahAI CEP Extension - ExtendScript Side
 * This file contains shared constants between the CEP panel and ExtendScript
 */

// File paths
var SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
var HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
var EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";

// API defaults
var DEFAULT_OLLAMA_URL = "http://localhost:11434";
var DEFAULT_LMSTUDIO_URL = "http://localhost:1234";

// HTTP settings
var DEFAULT_TIMEOUT = 10000;
var DEFAULT_RETRIES = 2;
var LOCAL_TIMEOUT = 8000;
var LOCAL_RETRIES = 1;
var API_TIMEOUT = 12000;
var API_RETRIES = 2;

// Export constants
module.exports = {
  SETTINGS_FILE_PATH: SETTINGS_FILE_PATH,
  HISTORY_FILE_PATH: HISTORY_FILE_PATH,
  EXTENSION_DIR_PATH: EXTENSION_DIR_PATH,
  DEFAULT_OLLAMA_URL: DEFAULT_OLLAMA_URL,
  DEFAULT_LMSTUDIO_URL: DEFAULT_LMSTUDIO_URL,
  DEFAULT_TIMEOUT: DEFAULT_TIMEOUT,
  DEFAULT_RETRIES: DEFAULT_RETRIES,
  LOCAL_TIMEOUT: LOCAL_TIMEOUT,
  LOCAL_RETRIES: LOCAL_RETRIES,
  API_TIMEOUT: API_TIMEOUT,
  API_RETRIES: API_RETRIES
};
